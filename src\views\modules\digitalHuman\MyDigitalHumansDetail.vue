<template>
	<div class="my-digital-humans-detail-app">
		<!-- 顶部导航条区域 -->
		<Headbar />

		<!-- 主要内容区域 -->
		<div
			class="main-content"
			ref="mainContentRef"
			@touchstart="handleTouchStart"
			@touchmove="handleTouchMove"
			@touchend="handleTouchEnd"
		>
			<!-- 下拉刷新指示器 -->
			<div
				class="pull-refresh-indicator"
				:class="{ 'visible': pullRefreshStatus !== 'idle' }"
				:style="{ transform: `translateY(${pullDistance}px)` }"
			>
				<div class="refresh-content">
					<el-icon class="refresh-icon" :class="{ 'rotating': pullRefreshStatus === 'refreshing' }">
						<Refresh />
					</el-icon>
					<span class="refresh-text">{{ refreshText }}</span>
				</div>
			</div>

			<div class="page-header">
				<div class="header-content">
					<h1>我的数字人</h1>
					<el-button
						type="default"
						@click="goToDigitalHumanTransition"
						class="back-button"
					>
						返回
					</el-button>
				</div>
			</div>

			<div class="content-area">
				<!-- 我的数字人展示区域 -->
				<div
					class="humans-container"
					v-infinite-scroll="loadMore"
					:infinite-scroll-disabled="loading || !hasMore"
					:infinite-scroll-distance="50"
				>
					<!-- 加载状态 -->
					<div v-if="loading && myDigitalHumansList.length === 0" class="loading-container">
						<el-icon class="is-loading">
							<Loading />
						</el-icon>
						<span>正在加载我的数字人...</span>
					</div>

					<!-- 错误状态 -->
					<div v-else-if="myDigitalHumansError" class="error-container">
						<span class="error-text">{{ myDigitalHumansError }}</span>
						<button class="retry-button" @click="handleRefresh">重试</button>
					</div>

					<!-- 数字人网格 -->
					<div v-else class="humans-grid">
						<div class="human-item" v-for="(item, index) in myDigitalHumansList" :key="item.id">
							<div class="human-avatar" @mouseenter="showCreateVideo(index)" @mouseleave="hideCreateVideo(index)">
								<!-- 使用从API获取的数字人图片 -->
								<img
									:src="item.picUrl || defaultAvatar"
									alt="我的数字人头像"
									@error="handleImageError($event, index)"
								/>
								<!-- 创建视频按钮 -->
								<div class="create-video-overlay" :class="{ show: hoveredIndex === index && !dropdownVisible && item.status === 'normal' }" @click="navigateToMyDigitalHumanEditor(item)">创建视频</div>

								<!-- 生成中状态 -->
								<div class="generating-overlay" v-if="item.status === 'generating'">
									<img :src="generatingGif" alt="生成中" />
									<p>生成中...{{ item.progress }}%</p>
								</div>

								<!-- 失败状态 -->
								<div class="failed-overlay" v-if="item.status === 'failed'">
									<!-- 删除图标 -->
									<img :src="deleteIcon" @click="deleteDigitalHuman(index)" class="delete-icon" />
									<!-- 失败标签 -->
									<div class="failure-label">失败</div>
									<!-- 重新编辑按钮 -->
									<div class="retry-edit-button" @click="retryEditDigitalHuman(item)">重新编辑</div>
								</div>

								<!-- 三个点菜单 -->
								<el-dropdown
									trigger="hover"
									placement="bottom"
									@command="handleCommand"
									@visible-change="handleDropdownVisibleChange"
									v-if="item.status !== 'generating' && item.status !== 'failed'"
									class="three-dots-dropdown">
									<div class="three-dots">⋮</div>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item :command="`rename-${index}`">重命名</el-dropdown-item>
											<el-dropdown-item :command="`delete-${index}`">删除</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
							</div>
							<div class="human-name">
								<!-- 编辑状态显示输入框 -->
								<el-input
									v-if="editingIndex === index"
									v-model="editingName"
									@blur="saveRename(index)"
									@keyup.enter="saveRename(index)"
									@keyup.esc="cancelRename"
									size="small"
									maxlength="20"
									show-word-limit
									ref="renameInputRef"
								/>
								<!-- 正常状态显示名称 -->
								<span v-else>{{ item.name }}</span>
							</div>
						</div>
					</div>

					<!-- 加载更多指示器 -->
					<div v-if="loading && myDigitalHumansList.length > 0" class="load-more-indicator">
						<el-icon class="is-loading">
							<Loading />
						</el-icon>
						<span>加载更多...</span>
					</div>

					<!-- 没有更多数据提示 -->
					<div v-if="!hasMore && myDigitalHumansList.length > 0" class="no-more-data">
						<span>已加载全部数据</span>
					</div>

					<!-- 空状态 -->
					<div v-if="!loading && !myDigitalHumansError && myDigitalHumansList.length === 0" class="empty-state">
						<img :src="emptyStateImage" alt="暂无数据" class="empty-image" />
						<p class="empty-text">暂无我的数字人</p>
						<el-button type="primary" @click="navigateToCreateDigitalHuman">创建数字人</el-button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Loading } from '@element-plus/icons-vue'

const router = useRouter()
const goToDigitalHumanTransition = () => {
  router.push('/digital-human-transition')
}

// 导入统一的Headbar组件
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
// 导入数字人相关API
import { getDigitalHumanListByUserId, batchDeleteDigitalHuman } from '@/api/digitalHuman.js'
import { updateWorksStatus } from '@/api/mySpace.js'
import { useloginStore } from '@/stores/login'
// 导入静态资源
import defaultAvatar from '@/assets/img/ceshi1.png'
import generatingGif from '@/assets/img/dongtai1.gif'
import deleteIcon from '@/assets/img/delete.png'
import emptyStateImage from '@/assets/img/empty.png'

// 获取用户ID
const loginStore = useloginStore()
const getUserId = () => {
    return loginStore.userId || ''
}

// ========================================
// 数据状态管理
// ========================================
const myDigitalHumansList = ref([]) // 我的数字人列表数据
const loading = ref(false) // 加载状态
const myDigitalHumansError = ref(null) // 错误状态
const currentPage = ref(1) // 当前页码
const pageSize = ref(20) // 每页数量
const hasMore = ref(true) // 是否还有更多数据

// UI交互状态
const hoveredIndex = ref(null) // 当前悬停的数字人索引
const dropdownVisible = ref(false) // 下拉菜单是否可见
const editingIndex = ref(null) // 正在编辑的数字人索引
const editingName = ref('') // 编辑中的名称
const renameInputRef = ref(null) // 重命名输入框引用

// 定时器管理
const myDigitalHumansTimer = ref(null) // 状态更新定时器

// ========================================
// 下拉刷新相关状态
// ========================================
const mainContentRef = ref(null) // 主内容区域引用
const pullRefreshStatus = ref('idle') // 下拉刷新状态：idle, pulling, ready, refreshing
const pullDistance = ref(0) // 下拉距离
const startY = ref(0) // 触摸开始Y坐标
const currentY = ref(0) // 当前触摸Y坐标
const isPulling = ref(false) // 是否正在下拉

// 下拉刷新阈值
const PULL_THRESHOLD = 60 // 触发刷新的下拉距离
const MAX_PULL_DISTANCE = 100 // 最大下拉距离

// 下拉刷新文本
const refreshText = computed(() => {
    switch (pullRefreshStatus.value) {
        case 'pulling':
            return '下拉刷新'
        case 'ready':
            return '释放刷新'
        case 'refreshing':
            return '刷新中...'
        default:
            return ''
    }
})

// ========================================
// 数据加载方法
// ========================================

// 获取我的数字人列表数据
const getMyDigitalHumansList = async (page = 1, isRefresh = false) => {
    try {
        loading.value = true
        myDigitalHumansError.value = null

        const userId = getUserId()
        if (!userId) {
            throw new Error('用户ID不存在')
        }

        console.log('开始获取我的数字人列表，用户ID:', userId, '页码:', page)

        const response = await getDigitalHumanListByUserId({
            page,
            size: pageSize.value,
            userId: userId
        })

        console.log('获取我的数字人列表API响应:', response)

        if (response && response.records && Array.isArray(response.records)) {
            // 数据转换：将API返回的数据转换为组件期望的格式
            const convertedList = response.records.map(item => ({
                id: item.id,
                name: item.name || '未命名数字人',
                picUrl: item.picUrl || null, // 数字人头像URL
                status: mapDigitalHumanStatus(item.status), // 状态映射
                progress: item.progress || 0, // 生成进度
                originalData: item // 保存原始数据以备后续使用
            }))

            if (isRefresh || page === 1) {
                // 刷新或首次加载，替换数据
                myDigitalHumansList.value = convertedList
            } else {
                // 加载更多，追加数据
                myDigitalHumansList.value = [...myDigitalHumansList.value, ...convertedList]
            }

            // 更新分页状态
            currentPage.value = page
            hasMore.value = response.records.length === pageSize.value // 如果返回数据等于页面大小，说明可能还有更多数据

            console.log(`获取我的数字人列表成功 - 第${page}页:`, convertedList.length, '条数据')

            // 启动生成中状态的定时更新
            startGeneratingStatusUpdate()
        } else {
            console.warn('API返回数据格式异常:', response)
            if (isRefresh || page === 1) {
                myDigitalHumansList.value = []
            }
        }

    } catch (error) {
        console.error('获取我的数字人列表失败:', error)
        myDigitalHumansError.value = error.message || '获取数字人列表失败'
        ElMessage.error('获取数据失败，请重试')
    } finally {
        loading.value = false
    }
}

// 加载更多数据
const loadMore = async () => {
    if (loading.value || !hasMore.value) return

    const nextPage = currentPage.value + 1
    await getMyDigitalHumansList(nextPage, false)
}

// 映射数字人状态：将API返回的数字状态映射为组件期望的字符串状态
const mapDigitalHumanStatus = (apiStatus) => {
    // 与"我的作品"模块保持一致的状态映射
    switch (String(apiStatus)) {
        case '0':
            return 'generating' // 生成中
        case '1':
            return 'normal'     // 成功/正常
        case '2':
            return 'failed'     // 失败
        default:
            console.warn('未知的数字人状态:', apiStatus)
            return 'normal'     // 默认为正常状态
    }
}

// ========================================
// 下拉刷新事件处理
// ========================================

// 触摸开始
const handleTouchStart = (e) => {
    if (mainContentRef.value && mainContentRef.value.scrollTop === 0) {
        startY.value = e.touches[0].clientY
        isPulling.value = true
    }
}

// 触摸移动
const handleTouchMove = (e) => {
    if (!isPulling.value) return

    currentY.value = e.touches[0].clientY
    const deltaY = currentY.value - startY.value

    if (deltaY > 0 && mainContentRef.value && mainContentRef.value.scrollTop === 0) {
        // 阻止默认滚动行为
        e.preventDefault()

        // 计算下拉距离，添加阻尼效果
        const damping = 0.5
        pullDistance.value = Math.min(deltaY * damping, MAX_PULL_DISTANCE)

        // 更新下拉状态
        if (pullDistance.value >= PULL_THRESHOLD) {
            pullRefreshStatus.value = 'ready'
        } else {
            pullRefreshStatus.value = 'pulling'
        }
    }
}

// 触摸结束
const handleTouchEnd = async () => {
    if (!isPulling.value) return

    isPulling.value = false

    if (pullRefreshStatus.value === 'ready') {
        // 触发刷新
        pullRefreshStatus.value = 'refreshing'
        await handleRefresh()
    }

    // 重置状态
    pullRefreshStatus.value = 'idle'
    pullDistance.value = 0
}

// 处理刷新
const handleRefresh = async () => {
    try {
        // 重置分页状态
        currentPage.value = 1
        hasMore.value = true

        // 重新加载第一页数据
        await getMyDigitalHumansList(1, true)

        ElMessage.success('刷新成功')
    } catch (error) {
        console.error('刷新失败:', error)
        ElMessage.error('刷新失败，请重试')
    }
}

// ========================================
// UI交互功能
// ========================================

// 显示创建视频按钮
const showCreateVideo = (index) => {
    // 如果下拉菜单正在显示，则不显示创建视频按钮
    if (dropdownVisible.value) {
        return
    }

    // 检查数字人状态，只有正常状态才显示创建视频按钮
    const digitalHuman = myDigitalHumansList.value[index]
    if (!digitalHuman || digitalHuman.status !== 'normal') {
        return // 失败状态、生成中状态或其他非正常状态不显示按钮
    }

    hoveredIndex.value = index
}

// 隐藏创建视频按钮
const hideCreateVideo = () => {
    hoveredIndex.value = null
}

// 处理下拉菜单命令
const handleCommand = (command) => {
    const [action, index] = command.split('-')
    const indexNum = parseInt(index)

    if (action === 'rename') {
        renameDigitalHuman(indexNum)
    } else if (action === 'delete') {
        deleteDigitalHuman(indexNum)
    }
}

// 处理下拉菜单显示状态变化
const handleDropdownVisibleChange = (visible) => {
    dropdownVisible.value = visible
    if (visible) {
        // 当下拉菜单显示时，隐藏创建视频按钮
        hoveredIndex.value = null
    }
}

// 重命名数字人
const renameDigitalHuman = (index) => {
    console.log(`重命名数字人 ${index + 1}`)
    editingIndex.value = index
    editingName.value = myDigitalHumansList.value[index].name // 设置为当前名字

    // 使用nextTick确保DOM更新后再聚焦
    nextTick(() => {
        if (renameInputRef.value && renameInputRef.value.length > 0) {
            renameInputRef.value[0].focus()
        }
    })
}

// 保存重命名
const saveRename = async (index) => {
    if (!editingName.value.trim()) {
        ElMessage.warning('数字人名称不能为空')
        return
    }

    try {
        // 这里应该调用重命名API，暂时只更新本地数据
        myDigitalHumansList.value[index].name = editingName.value.trim()
        ElMessage.success('重命名成功')
    } catch (error) {
        console.error('重命名失败:', error)
        ElMessage.error('重命名失败，请重试')
    } finally {
        // 重置编辑状态
        editingIndex.value = null
        editingName.value = ''
    }
}

// 取消重命名
const cancelRename = () => {
    editingIndex.value = null
    editingName.value = ''
}

// 删除数字人
const deleteDigitalHuman = async (index) => {
    try {
        const digitalHuman = myDigitalHumansList.value[index]

        // 确认删除
        await ElMessageBox.confirm(
            `确定要删除数字人"${digitalHuman.name}"吗？`,
            '删除确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )

        // 确保ID被包装在数组中，无论ID是字符串还是数字
        const deleteParams = {
            ids: [digitalHuman.id]  // 将ID包装在数组中
        }

        console.log('删除参数:', deleteParams)

        // 调用批量删除接口
        await batchDeleteDigitalHuman(deleteParams)

        // 删除成功后更新UI
        myDigitalHumansList.value.splice(index, 1)
        ElMessage.success('删除成功')

    } catch (error) {
        if (error !== 'cancel') { // 用户取消删除不显示错误信息
            console.error('删除数字人失败:', error)
            ElMessage.error('删除失败，请重试')
        }
    }
}

// ========================================
// 导航和跳转功能
// ========================================

// 导航到我的数字人编辑器
const navigateToMyDigitalHumanEditor = (item) => {
    console.log('导航到数字人编辑器，数字人数据:', item)

    // 将我的数字人数据转换为编辑器期望的格式
    const digitalHumanData = {
        id: item.id,
        name: item.name,
        figures: [{
            type: 'main_view',
            cover: item.picUrl || defaultAvatar
        }],
        originalData: item.originalData
    }

    // 跳转到数字人编辑器页面，传递数字人数据
    router.push({
        path: '/digital-human-editor-page',
        query: {
            digitalHumanData: JSON.stringify(digitalHumanData),
            digitalHumanName: item.name,
            from: '/my-digital-humans-detail'
        }
    })
}

// 重新编辑失败的数字人
const retryEditDigitalHuman = (item) => {
    console.log('重新编辑数字人:', item)
    // 跳转到数字人创建页面进行重新编辑
    router.push({
        path: '/videoDigitalHuman',
        query: {
            retryId: item.id,
            retryName: item.name
        }
    })
}

// 导航到创建数字人页面
const navigateToCreateDigitalHuman = () => {
    router.push('/videoDigitalHuman')
}

// 处理图片加载错误
const handleImageError = (event, index) => {
    console.log(`数字人 ${index + 1} 图片加载失败，使用默认图片`)
    // 图片加载失败时，使用默认图片
    event.target.src = defaultAvatar
}

// ========================================
// 状态更新管理
// ========================================

// 启动生成中状态的定时更新
const startGeneratingStatusUpdate = () => {
    // 清除之前的定时器
    if (myDigitalHumansTimer.value) {
        clearInterval(myDigitalHumansTimer.value)
        myDigitalHumansTimer.value = null
    }

    // 查找生成中的数字人
    const generatingList = myDigitalHumansList.value.filter(item => item.status === 'generating')

    if (generatingList.length > 0) {
        console.log('发现生成中的数字人，启动状态更新定时器:', generatingList.length, '个')

        // 每30秒更新一次状态，与"我的作品"模块保持一致
        myDigitalHumansTimer.value = setInterval(() => {
            updateMyDigitalHumansStatus()
        }, 30000)
    }
}

// 更新我的数字人状态
const updateMyDigitalHumansStatus = async () => {
    try {
        // 获取生成中的数字人ID列表
        const generatingList = myDigitalHumansList.value.filter(item => item.status === 'generating')
        const ids = generatingList.map(item => item.id)

        if (ids.length === 0) {
            // 没有生成中的数字人，清除定时器
            if (myDigitalHumansTimer.value) {
                clearInterval(myDigitalHumansTimer.value)
                myDigitalHumansTimer.value = null
            }
            return
        }

        console.log('更新数字人状态，ID列表:', ids)

        // 调用状态更新接口
        const updatedData = await updateWorksStatus({ ids })

        if (updatedData && Array.isArray(updatedData)) {
            // 更新本地数据状态
            updatedData.forEach(updatedItem => {
                const localIndex = myDigitalHumansList.value.findIndex(item => item.id === updatedItem.id)
                if (localIndex !== -1) {
                    const newStatus = mapDigitalHumanStatus(updatedItem.status)
                    myDigitalHumansList.value[localIndex].status = newStatus
                    myDigitalHumansList.value[localIndex].progress = updatedItem.progress || 0

                    console.log(`数字人 ${updatedItem.id} 状态更新为: ${newStatus}`)
                }
            })

            // 检查是否还有生成中的数字人
            const stillGenerating = myDigitalHumansList.value.filter(item => item.status === 'generating')
            if (stillGenerating.length === 0) {
                // 没有生成中的数字人了，清除定时器
                if (myDigitalHumansTimer.value) {
                    clearInterval(myDigitalHumansTimer.value)
                    myDigitalHumansTimer.value = null
                    console.log('所有数字人生成完成，清除状态更新定时器')
                }
            }
        }
    } catch (error) {
        console.error('更新数字人状态失败:', error)
    }
}

// ========================================
// 生命周期管理
// ========================================

// 页面挂载时的初始化逻辑
onMounted(() => {
    console.log('我的数字人详情页面已加载')
    // 获取我的数字人列表（首次加载）
    getMyDigitalHumansList(1, true)
})

// 页面卸载时的清理逻辑
onUnmounted(() => {
    // 清除定时器
    if (myDigitalHumansTimer.value) {
        clearInterval(myDigitalHumansTimer.value)
        myDigitalHumansTimer.value = null
    }
})
</script>

<style scoped lang="scss">
// 全屏应用容器
.my-digital-humans-detail-app {
	display: flex;
	flex-direction: column;
	overflow: hidden;
	background-color: #FFFFFF;
}

// 主要内容区域
.main-content {
	flex: 1;
	padding: 20px 20px 20px 78px;
	margin-top: 64px; // 为Headbar组件预留空间
	overflow-y: auto;
	background-color: #FFFFFF;
	position: relative; // 为下拉刷新指示器定位
}

// ========================================
// 下拉刷新相关样式
// ========================================
.pull-refresh-indicator {
	position: absolute;
	top: -60px;
	left: 0;
	right: 0;
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #FFFFFF;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
	z-index: 10;

	&.visible {
		opacity: 1;
		visibility: visible;
	}

	.refresh-content {
		display: flex;
		align-items: center;
		gap: 8px;
		color: #0AAF60;
		font-size: 14px;
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;

		.refresh-icon {
			font-size: 16px;
			transition: transform 0.3s ease;

			&.rotating {
				animation: rotate 1s linear infinite;
			}
		}
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

// 页面头部
.page-header {
	margin-bottom: 30px;

	.header-content {
		display: flex;
		align-items: center;
		gap: 40px; // 按钮与标题间距40px
	}

	h1 {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 32px;
		font-weight: 500;
		color: #333;
		margin: 0;
	}

	.back-button {
		// 设置按钮样式：白色背景，绿色边框和文字
		&.el-button--default {
			background-color: #FFFFFF;
			border-color: #0AAF60;
			color: #0AAF60;

			&:hover, &:focus {
				background-color: #f0f9f0; // 浅绿色背景
				border-color: #0AAF60;
				color: #0AAF60;
			}
		}
	}
}

// 内容区域
.content-area {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0; // 确保flex子项能够正确收缩
}

// 我的数字人容器
.humans-container {
	flex: 1;
	width: 1767px !important; // 保持固定宽度
	height: 100%; // 明确设置高度
	overflow: visible; // 显示所有内容，不隐藏
	display: flex;
	flex-direction: column;
}

// 加载状态样式
.loading-container {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	height: 200px;
	gap: 16px;

	.is-loading {
		font-size: 24px;
		color: #0AAF60;
		animation: rotate 1s linear infinite;
	}

	span {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 16px;
		color: #666;
	}
}

// 错误状态样式
.error-container {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	height: 200px;
	gap: 16px;

	.error-text {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 16px;
		color: #ff4d4f;
	}

	.retry-button {
		padding: 8px 16px;
		background: #0AAF60;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 14px;
		transition: background-color 0.3s ease;

		&:hover {
			background: #099954;
		}
	}
}

// 数字人网格样式
.humans-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start; // 从左往右依次排列，保持固定间距
	gap: 69px;
	width: 1535px;
}

.human-item {
	text-align: center;
	cursor: pointer;
	width: 160px; // 固定宽度，与图片宽度一致
	flex-shrink: 0; // 防止收缩
}

.human-avatar {
	position: relative;
	width: 160px;
	height: auto;
	aspect-ratio: 1/1.3;
	border-radius: 8px;
	overflow: hidden;
	background: #f0f0f0;

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		border-radius: 8px;
	}
}

.create-video-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 84px;
	height: 32px;
	background: #0AAF60;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	font-weight: 500;
	border-radius: 4px;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
	cursor: pointer;

	&:hover {
		background: #099954;
	}
}

.create-video-overlay.show {
	opacity: 1;
	visibility: visible;
}

// 三个点菜单样式
.three-dots-dropdown {
	position: absolute;
	top: 8px;
	right: 8px;
	z-index: 100;
}

.three-dots {
	width: 24px;
	height: 24px;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 16px;
	font-weight: bold;
	transition: background-color 0.3s ease;
	box-shadow: none !important;
	border: none !important;
	outline: none !important;
}

// 覆盖Element Plus的默认样式
.three-dots-dropdown {
	.el-tooltip__trigger {
		box-shadow: none !important;
		border: none !important;
		outline: none !important;
	}
}

// 生成中状态样式
.generating-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: #F1F2F4;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	border-radius: 8px;

	img {
		width: 60px;
		height: 60px;
		margin-bottom: 8px;
	}

	p {
		font-size: 14px;
		color: #333;
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		margin: 0;
	}
}

// 失败状态样式 - 与"我的作品"模块保持一致
.failed-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.4); // 半透明遮罩，与"我的作品"一致
	border-radius: 8px;

	// 右上角删除图标
	.delete-icon {
		position: absolute;
		top: 8px;
		right: 8px;
		width: 16px;
		height: 16px;
		cursor: pointer;
		z-index: 10;

		&:hover {
			opacity: 0.8;
		}
	}

	// 左下角失败标签
	.failure-label {
		position: absolute;
		bottom: 7px;
		left: 7px;
		width: 40px;
		height: 21px;
		background: #FF2D55; // 与"我的作品"一致的红色
		border-radius: 2px;
		font-size: 12px;
		color: #FFFFFF;
		text-align: center;
		line-height: 21px;
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
	}

	// 中间重新编辑按钮
	.retry-edit-button {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 84px;
		height: 32px;
		background: transparent;
		color: #FFFFFF;
		border: 1px solid #FFFFFF;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.3s ease;
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;

		&:hover {
			background: rgba(255, 255, 255, 0.1);
		}
	}
}

.human-name {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}

// 加载更多指示器
.load-more-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
	padding: 20px;
	color: #666;
	font-size: 14px;
	font-family: 'Alibaba PuHuiTi 2.0', sans-serif;

	.is-loading {
		font-size: 16px;
		color: #0AAF60;
		animation: rotate 1s linear infinite;
	}
}

// 没有更多数据提示
.no-more-data {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
	color: #999;
	font-size: 14px;
	font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
}

// 空状态样式
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400px;
	gap: 20px;

	.empty-image {
		width: 120px;
		height: 120px;
		opacity: 0.6;
	}

	.empty-text {
		font-size: 16px;
		color: #999;
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		margin: 0;
	}

	.el-button {
		margin-top: 10px;
	}
}

// 响应式设计
@media (max-width: 1920px) {
	.humans-container {
		width: 100% !important;
	}

	.humans-grid {
		width: 100%;
		justify-content: flex-start;
		gap: 40px;
	}
}

@media (max-width: 1600px) {
	.humans-grid {
		gap: 30px;
	}

	.human-item {
		width: 180px;
	}

	.human-avatar {
		width: 180px;
		height: 180px;
	}
}

@media (max-width: 1200px) {
	.humans-grid {
		gap: 20px;
	}

	.human-item {
		width: 160px;
	}

	.human-avatar {
		width: 160px;
		height: 160px;
	}
}

@media (max-width: 768px) {
	.main-content {
		padding: 15px;
		margin-top: 56px;
	}

	.page-header h1 {
		font-size: 24px;
	}

	.page-header .header-content {
		gap: 20px;
	}

	.humans-grid {
		gap: 15px;
		justify-content: center;
	}

	.human-item {
		width: 140px;
	}

	.human-avatar {
		width: 140px;
		height: 140px;
	}

	.create-video-overlay {
		width: 70px;
		height: 28px;
		font-size: 12px;
	}

	.retry-edit-button {
		width: 70px;
		height: 28px;
		font-size: 12px;
	}
}

@media (max-width: 480px) {
	.humans-grid {
		gap: 10px;
	}

	.human-item {
		width: 120px;
	}

	.human-avatar {
		width: 120px;
		height: 120px;
	}

	.human-name {
		font-size: 12px;
	}
}
</style>